# export ARK_API_KEY="YOUR_API_KEY" 查看API KEY 

import os
import base64
import json
from volcenginesdkarkruntime import Ark
import datetime
import re
import shutil
import markdown
from bs4 import BeautifulSoup
from pypinyin import pinyin, Style
from PIL import Image
import io
import numpy as np
import multiprocessing  # 新增：导入多进程模块
import time  # 新增：用于记录时间
from tqdm import tqdm  # 新增：导入进度条模块

from image_utils import image_to_base64, validate_base64
from yolo_utils import parse_yolo_annotation
from ai_utils import extract_json_from_response

def process_single_image_local(task):
    """
    处理单张图片的本地操作（增强、缩放、base64编码）
    """
    image_path = task['image_path']
    img_filename = task['img_filename']
    use_enhance = task['use_enhance']
    enhance_threshold = task['enhance_threshold']
    scale = task['scale']
    use_pixel_connection = task['use_pixel_connection']

    try:
        base64_image = image_to_base64(image_path, use_enhance=use_enhance,
                                     enhance_threshold=enhance_threshold,
                                     scale=scale, use_pixel_connection=use_pixel_connection)
        return {
            'success': True,
            'img_filename': img_filename,
            'base64_image': base64_image,
            'image_path': image_path
        }
    except Exception as e:
        return {
            'success': False,
            'img_filename': img_filename,
            'base64_image': None,
            'image_path': image_path,
            'error': str(e)
        }

def enhance_marks_to_black(image_data, threshold=200):
    """
    将图片中的灰色或浅黑色标记增强为纯黑色，并将背景转换为纯白色。
    threshold: 阈值，低于该值的像素变为黑色，否则为白色
    """
    # 从内存中的图片数据创建Image对象
    image = Image.open(io.BytesIO(image_data))
    # 1. 转换为灰度图像
    grayscale_image = image.convert('L')
    # 2. 应用阈值，二值化
    threshold_image = grayscale_image.point(lambda p: 0 if p < threshold else 255)
    # 3. 转回RGB（如有需要）
    final_image = threshold_image.convert('RGB')
    # 4. 保存到内存
    buffer = io.BytesIO()
    final_image.save(buffer, format='JPEG')
    return buffer.getvalue()

def get_image_files(images_dir):
    """获取images文件夹中的所有图片文件，按文件名字典序排序"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    image_files = []
    if os.path.exists(images_dir):
        for filename in os.listdir(images_dir):
            if any(filename.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(images_dir, filename))
    # 按文件名字典序排序
    return sorted(image_files)

def markdown_to_text(markdown_content):
    """将markdown格式转换为纯文本"""
    # 将markdown转换为HTML
    html = markdown.markdown(markdown_content)
    # 使用BeautifulSoup提取纯文本
    soup = BeautifulSoup(html, 'html.parser')
    # 获取纯文本内容
    text = soup.get_text()
    # 清理多余的空白字符
    text = re.sub(r'\n\s*\n', '\n\n', text)  # 将多个空行替换为两个换行
    text = re.sub(r'[ \t]+', ' ', text)  # 将多个空格替换为单个空格
    return text.strip()

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_latest_folder(base_dir, prefix="images_"):
    """获取指定目录下以prefix开头的最新文件夹"""
    if not os.path.exists(base_dir):
        return None

    folders = []
    for item in os.listdir(base_dir):
        item_path = os.path.join(base_dir, item)
        if os.path.isdir(item_path) and item.startswith(prefix):
            folders.append(item)

    if not folders:
        return None

    # 按文件夹名排序，取最新的（字典序最大的）
    folders.sort()
    return folders[-1]

def get_folder_choice(question_dir):
    """让用户选择图像文件夹"""
    folder_options = {
        "1": "images",
        "2": "OpenCV_result",
        "3": "grounding_result",
        "4": "YOLO_result",
        "5": "YOLO_text_result",
        "6": "manual_result",
        "7": "roboflow_yolo_result"
    }

    print("\n请选择图像文件夹：")
    for key, value in folder_options.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入文件夹编号（1-7）：").strip()

        if user_input in folder_options:
            folder_name = folder_options[user_input]
            break
        else:
            print("输入无效，请输入 1-7 的数字")

    # 如果选择的是结果文件夹，需要找到最新的子文件夹
    if folder_name != "images":
        result_dir = os.path.join(question_dir, folder_name)
        latest_folder = get_latest_folder(result_dir)
        if latest_folder:
            actual_images_dir = os.path.join(result_dir, latest_folder)
            print(f"选择的文件夹：{folder_name}")
            print(f"最新的子文件夹：{latest_folder}")
            print(f"实际图片路径：{actual_images_dir}")
            # 返回相对于question_dir的路径，用于生成正确的markdown图片路径
            relative_path = os.path.join(folder_name, latest_folder).replace("\\", "/")
            return actual_images_dir, relative_path
        else:
            print(f"错误：{folder_name} 文件夹下没有找到任何图片文件夹！")
            return None, None
    else:
        images_dir = os.path.join(question_dir, "images")
        print(f"选择的文件夹：images")
        print(f"实际图片路径：{images_dir}")
        return images_dir, "images"

def get_model_choice():
    """让用户选择模型ID"""
    available_models = {
        "1": "doubao-seed-1-6-250615",
        "2": "doubao-seed-1-6-flash-250715",
        "3": "doubao-1-5-thinking-vision-pro-250428",
        "4": "doubao-1-5-vision-pro-32k-250115"
    }

    print("请选择模型ID：")
    for key, value in available_models.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入模型编号（1-4）：").strip()
        if user_input in available_models:
            selected_model = available_models[user_input]
            print(f"选择的模型：{selected_model}")
            return selected_model
        else:
            print("输入无效，请输入 1-4 的数字")


def get_response_format_choice(model_id):
    """根据模型ID判断是否支持jsonObject，如果支持则让用户选择response_format"""
    # 支持jsonObject的模型列表（模型1、2、3）
    json_object_supported_models = [
        "doubao-seed-1-6-250615",
        "doubao-seed-1-6-flash-250715",
        "doubao-1-5-thinking-vision-pro-250428"
    ]

    if model_id in json_object_supported_models:
        print("选择response_format：")
        print("1. text")
        print("2. json_object")

        while True:
            user_input = input("请输入选择（1-2）：").strip()
            if user_input == "1":
                return "text"
            elif user_input == "2":
                return "json_object"
            else:
                print("输入无效，请输入 1 或 2")
    else:
        return "text"  # 不支持jsonObject的模型默认使用text

def get_max_tokens_for_model(model_id):
    """根据模型ID返回对应的max_tokens值"""
    if model_id == "doubao-1-5-vision-pro-32k-250115":  # 模型4
        return 12288  # 12K
    else:  # 模型1、2、3
        return 16384  # 16K

def get_question_type():
    """获取用户输入的题型并转换为拼音路径"""
    question_types = {
        "1": "涂卡选择题",
        "2": "涂卡判断题",
        "3": "连线题",
        "4": "图表题",
        "5": "翻译题",
        "6": "画图题",
        "7": "数学应用题",
        "8": "数学计算题",
        "9": "简单的四则运算",
        "10": "填空题",
        "11": "判断题",
        "12": "多选题",
        "13": "单选题"
    }

    print("请选择题型：")
    for key, value in question_types.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入题型编号（1-13）或直接输入中文：").strip()

        # 检查数字输入
        if user_input in question_types:
            question_type = question_types[user_input]
            break
        # 检查中文输入
        elif user_input in question_types.values():
            question_type = user_input
            break
        else:
            print("输入无效，请输入 1-13 的数字或直接输入中文题型名称")

    # 转换为拼音
    pinyin_name = chinese_to_pinyin(question_type)
    print(f"选择的题型：{question_type}")
    print(f"对应的拼音路径：{pinyin_name}")

    return question_type, pinyin_name

def extract_json_responses(md_path):
    """提取md文件中所有响应内容的JSON"""
    results = []
    with open(md_path, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 匹配所有 ### 响应内容： 后面紧跟的代码块内容
    resp_pattern = r"### 响应内容：\s*```json\s*([\s\S]*?)\s*```"
    resp_matches = re.findall(resp_pattern, content)
    
    for match in resp_matches:
        json_str = match.strip()
        try:
            # 验证JSON格式
            json.loads(json_str) # Just load to validate, don't store object
            results.append(json_str)
        except json.JSONDecodeError as e: # More specific error handling
            print(f"警告：跳过无效的JSON格式 '{json_str[:50]}...' (错误: {str(e)})")
        except Exception as e:
            print(f"警告：跳过无效的JSON格式，未知错误: {str(e)}")
    
    return results

def process_single_image_one_stage_api(task):
    """
    一站式API推理，输入为task字典（包含base64、图片名、one_stage_prompt、answer_json等）
    """
    from volcenginesdkarkruntime import Ark
    import os
    img_filename = task['img_filename']
    base64_image = task['base64_image']
    one_stage_prompt = task['one_stage_prompt']
    answer_json = task['answer_json']
    client_api_key = task['client_api_key']
    model_id = task.get('model_id', 'doubao-seed-1-6-flash-250715')  # 支持模型参数
    response_format = task.get('response_format', 'text')  # 获取response_format参数
    index = task['index']
    image_path_prefix = task['image_path_prefix']
    # 获取API参数，如果没有传入则使用默认值
    temperature = task.get('temperature', 1)
    top_p = task.get('top_p', 0.7)
    max_tokens_param = task.get('max_tokens')  # 如果没有传入则为None，后面会使用默认逻辑
    sep = f"\n{'=' * 50}\n"
    info = f"处理第 {index} 张图片: {img_filename}"
    current_image_output_lines = []
    current_image_output_lines.append(sep)
    current_image_output_lines.append(info + "\n")
    current_image_output_lines.append(sep)
    current_image_output_lines.append(f"![{img_filename}]({image_path_prefix}{img_filename})\n")
    # 移除控制台输出，改为使用进度条显示
    # print(f"[PID {os.getpid()}] 处理第 {index} 张图片: {img_filename}")
    try:
        client_local = Ark(
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key=client_api_key,
        )
        # 记录开始时间
        start_time = time.time()
        
        # 构建请求内容，包含图片、one_stage_prompt和answer_json
        content = [
            {"type": "image_url", "image_url": {"url": base64_image, "detail": "high"}},
            {"type": "text", "text": one_stage_prompt},
            {"type": "text", "text": f"正确答案json: {answer_json}"}
        ]
        
        # 构建未加密的显示版本内容（省略base64图片内容）
        content_display = []
        for item in content:
            if item.get("type") == "image_url":
                # 省略base64图片内容
                url = item["image_url"].get("url", "")
                if url.startswith("data:image/") and "," in url:
                    prefix, base64_data = url.split(",", 1)
                    if len(base64_data) > 10:
                        display_url = f"{prefix},{base64_data[:10]}...[省略{len(base64_data)-10}个字符]"
                    else:
                        display_url = url
                else:
                    display_url = url
                content_display.append({
                    "type": "image_url",
                    "image_url": {"url": display_url, "detail": "high"}
                })
            else:
                # 对于text类型，直接使用原始内容（未加密）
                content_display.append(item.copy())

        # 构建请求参数（先保存未加密版本用于显示）
        request_params_display = {
            "model": model_id,
            "messages": [
                {
                    "role": "user",
                    "content": content_display
                }
            ],
            "max_tokens": max_tokens_param if max_tokens_param is not None else get_max_tokens_for_model(model_id),
            "thinking": {"type": "disabled"}
        }

        # 只有当temperature不为None时才添加到请求体中
        if temperature is not None:
            request_params_display["temperature"] = temperature

        # 只有当top_p不为None时才添加到请求体中
        if top_p is not None:
            request_params_display["top_p"] = top_p

        # 构建实际请求参数（包含加密头）
        request_params = {
            "model": model_id,
            "messages": [
                {
                    "role": "user",
                    "content": content
                }
            ],
            "extra_headers": {'x-is-encrypted': 'true'},
            "max_tokens": max_tokens_param if max_tokens_param is not None else get_max_tokens_for_model(model_id),
            "thinking": {"type": "disabled"}
        }

        # 只有当temperature不为None时才添加到请求体中
        if temperature is not None:
            request_params["temperature"] = temperature

        # 只有当top_p不为None时才添加到请求体中
        if top_p is not None:
            request_params["top_p"] = top_p

        # 如果选择了json_object格式，添加response_format参数
        if response_format == "json_object":
            request_params["response_format"] = {"type": "json_object"}
            request_params_display["response_format"] = {"type": "json_object"}

        response = client_local.chat.completions.create(**request_params)
        # 记录结束时间并计算响应时间
        end_time = time.time()
        response_time = end_time - start_time
        resp_content = response.choices[0].message.content.strip()
        if resp_content.startswith("```json"):
            resp_content = resp_content[7:]
        if resp_content.startswith("```"):
            resp_content = resp_content[3:]
        if resp_content.endswith("```"):
            resp_content = resp_content[:-3]
        resp_content = resp_content.strip()
        
        # 添加正确答案JSON
        current_image_output_lines.append(f"### 正确答案：\n")
        current_image_output_lines.append("```json\n")
        current_image_output_lines.append(f"{answer_json}\n")
        current_image_output_lines.append("```\n\n")
        
        current_image_output_lines.append(f"### 响应内容：\n")
        current_image_output_lines.append("```json\n")
        current_image_output_lines.append(f"{resp_content}\n")
        current_image_output_lines.append("```\n")

        # 添加请求体（使用未加密版本，已经省略了base64图片内容）
        current_image_output_lines.append(f"### 请求体：\n")
        current_image_output_lines.append("```json\n")
        current_image_output_lines.append(f"{json.dumps(request_params_display, ensure_ascii=False, indent=2)}\n")
        current_image_output_lines.append("```\n")

        # 添加响应时间记录
        current_image_output_lines.append(f"### 响应时间：{response_time:.2f}秒\n")
        # token用量信息
        usage = getattr(response, 'usage', None)
        total_tokens = usage.total_tokens if usage and hasattr(usage, 'total_tokens') else None
        cached_tokens = None
        reasoning_tokens = None
        if usage:
            if hasattr(usage, 'prompt_tokens_details') and usage.prompt_tokens_details:
                cached_tokens = getattr(usage.prompt_tokens_details, 'cached_tokens', None)
            if hasattr(usage, 'completion_tokens_details') and usage.completion_tokens_details:
                reasoning_tokens = getattr(usage.completion_tokens_details, 'reasoning_tokens', None)
        current_image_output_lines.append("### token用量\n")
        current_image_output_lines.append(f"- total_tokens: {total_tokens}\n")
        current_image_output_lines.append(f"- cached_tokens: {cached_tokens}\n")
        current_image_output_lines.append(f"- reasoning_tokens: {reasoning_tokens}\n")
        return {
            'success': True,
            'image_path': task['image_path'],
            'output_lines': current_image_output_lines,
            'response_content': resp_content
        }
    except Exception as e:
        err_msg = f"处理图片 {img_filename} 时出错: {str(e)}"
        # 只在失败时打印错误信息
        print(f"[PID {os.getpid()}] 处理图片 {img_filename} 时出错: {str(e)}")
        current_image_output_lines.append(err_msg + "\n")
        return {
            'success': False,
            'image_path': task['image_path'],
            'output_lines': current_image_output_lines,
            'response_content': None
        }

def extract_json_responses_and_names(md_path):
    """提取md文件中所有图片名和响应内容（返回列表: [(img_name, json_str)]）"""
    results = []
    with open(md_path, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查文件类型：是图片处理结果还是JSON比对结果
    if "处理第" in content and "张图片:" in content:
        # 图片处理结果格式
        img_pattern = r"处理第 (\d+) 张图片: ([^\n]+)"
        img_matches = re.findall(img_pattern, content)
        # 匹配所有 ### 响应内容： 后面紧跟的代码块内容
        resp_pattern = r"### 响应内容：\s*```json\s*([\s\S]*?)\s*```"
        resp_matches = re.findall(resp_pattern, content)
    else:
        # JSON比对结果格式
        img_pattern = r"处理第 (\d+) 组JSON响应"
        img_matches = re.findall(img_pattern, content)
        # 匹配所有 ### 模型回答： 后面紧跟的代码块内容
        resp_pattern = r"### 模型回答：\s*```json\s*([\s\S]*?)\s*```"
        resp_matches = re.findall(resp_pattern, content)
    
    # 标准化json
    norm_jsons = []
    for m in resp_matches:
        json_str = m.strip()
        try:
            obj = json.loads(json_str)
            # 按照题号数字顺序排序，而不是字符串顺序
            def extract_question_number(key):
                """从题号中提取数字部分"""
                import re
                # 匹配数字部分
                match = re.search(r'\d+', str(key))
                if match:
                    return int(match.group())
                return 0  # 如果没有数字，返回0

            # 按照题号数字排序
            sorted_items = sorted(obj.items(), key=lambda x: extract_question_number(x[0]))
            sorted_obj = dict(sorted_items)
            norm = json.dumps(sorted_obj, ensure_ascii=False, separators=(',', ':'))
            norm_jsons.append(norm)
        except Exception:
            norm_jsons.append(json_str)
    
    # 按顺序配对
    for i in range(max(len(img_matches), len(norm_jsons))):
        if "张图片:" in content:
            # 图片处理结果
            img_name = img_matches[i][1] if i < len(img_matches) else None
        else:
            # JSON比对结果
            img_name = f"第{img_matches[i]}组" if i < len(img_matches) else None
        norm_json = norm_jsons[i] if i < len(norm_jsons) else None
        results.append((img_name, norm_json))
    return results

def run_one_stage_test(model_id=None, response_format=None, question_type=None, pinyin_name=None, images_dir=None, use_enhance=None, scale=None, use_pixel_connection=None, custom_prompt=None, temperature=None, top_p=None, max_tokens=None, gray_threshold=None):
    """
    运行一站式测试，支持从外部传入模型ID和题型
    """
    # 请确保您已将 API Key 存储在环境变量 ARK_API_KEY 中
    # 初始化Ark客户端，从环境变量中读取您的API Key
    client = Ark(
        # 此为默认路径，您可根据业务所在地域进行配置
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        # 从环境变量中获取您的 API Key。此为默认方式，您可根据需要进行修改
        api_key="36c2aa0e-8b2b-4412-bc92-d3d1cef96b1b",
    )

    # 如果没有传入模型ID，则获取用户选择的模型
    if model_id is None:
        model_id = get_model_choice()

    # 如果没有传入response_format，则获取用户选择的response_format
    if response_format is None:
        response_format = get_response_format_choice(model_id)

    # 如果没有传入题型，则获取用户选择的题型
    if question_type is None or pinyin_name is None:
        question_type, pinyin_name = get_question_type()

    print(f"使用模型: {model_id}")

    # 构建题型相关的路径
    types_dir = "types"
    question_dir = os.path.join(types_dir, pinyin_name)

    # 让用户选择图像文件夹
    if images_dir is None:
        images_dir, image_path_prefix = get_folder_choice(question_dir)
        if images_dir is None:
            print("无法获取图像文件夹，程序退出")
            exit()
    else:
        images_dir = os.path.join(question_dir, images_dir)
        image_path_prefix = images_dir.replace(question_dir, "").replace("\\", "/")
        print(f"使用外部传入的图片文件夹：{images_dir}")
        print(f"实际图片路径前缀：{image_path_prefix}")

    # 创建one_stage_response文件夹
    one_stage_response_dir = os.path.join(question_dir, "one_stage_response")
    os.makedirs(one_stage_response_dir, exist_ok=True)

    # 创建one_stage_error文件夹
    one_stage_error_dir = os.path.join(question_dir, "one_stage_error")
    os.makedirs(one_stage_error_dir, exist_ok=True)

    # 文件路径
    one_stage_prompt_file = os.path.join(question_dir, "one_stage_prompt.md")
    answer_file = os.path.join(question_dir, "response", "answer.md")

    # 如果没有传入use_enhance参数，则询问用户
    if use_enhance is None:
        while True:
            enhance_input = input("是否采用'灰度阀门与像素增强'处理？(y/n)：").strip().lower()
            if enhance_input in ("y", "n"):
                use_enhance = (enhance_input == "y")
                break
            else:
                print("请输入 y 或 n")

    # 如果选择了灰度阀门与像素增强，询问是否采用黑色像素粘连
    if use_pixel_connection is None:
        use_pixel_connection = False
        if use_enhance:
            while True:
                connection_input = input("是否采用'黑色像素粘连'处理？(y/n)：").strip().lower()
                if connection_input in ("y", "n"):
                    use_pixel_connection = (connection_input == "y")
                    break
                else:
                    print("请输入 y 或 n")

    # 如果没有传入scale参数，则询问用户
    if scale is None:
        while True:
            try:
                scale_input = input("请输入图片放大倍数（如2、4、6、8）：").strip()
                scale = float(scale_input)
                if scale <= 0:
                    print("放大倍数必须为正数！")
                    continue
                break
            except Exception:
                print("请输入有效的数字倍数！")

    # 如果没有传入use_pixel_connection参数，设置默认值
    if use_pixel_connection is None:
        use_pixel_connection = False

    # 处理灰度阀门参数
    if gray_threshold is None:
        gray_threshold = 200  # 使用默认值200
    else:
        # 验证灰度阀门值是否在有效范围内
        if not isinstance(gray_threshold, (int, float)) or not (0 <= gray_threshold <= 255):
            print(f"警告：灰度阀门值 {gray_threshold} 无效，必须在0-255范围内，将使用默认值200")
            gray_threshold = 200

    print(f"\n使用路径：")
    print(f"图片文件夹：{images_dir}")
    print(f"one_stage_response文件夹：{one_stage_response_dir}")
    print(f"one_stage_prompt文件：{one_stage_prompt_file}")
    print(f"answer文件：{answer_file}")
    print(f"one_stage_error文件夹：{one_stage_error_dir}")

    # 检查并创建必要的目录
    os.makedirs(images_dir, exist_ok=True)
    os.makedirs(one_stage_response_dir, exist_ok=True)

    # 检查必要文件是否存在
    if not custom_prompt and not os.path.exists(one_stage_prompt_file):
        print(f"错误：one_stage_prompt文件 {one_stage_prompt_file} 不存在！")
        print(f"请创建 {one_stage_prompt_file} 文件并写入提示词内容")
        exit()

    if not os.path.exists(answer_file):
        print(f"错误：answer文件 {answer_file} 不存在！")
        exit()

    # 优先使用自定义prompt，否则从one_stage_prompt.md文件读取提示词
    if custom_prompt:
        one_stage_prompt = custom_prompt
        print("使用从main脚本传递的自定义提示词")
    else:
        try:
            with open(one_stage_prompt_file, 'r', encoding='utf-8') as f:
                one_stage_markdown_prompt = f.read().strip()
            print(f"已从文件 {one_stage_prompt_file} 读取one_stage_prompt")
            # 将markdown格式转换为纯文本
            one_stage_prompt = markdown_to_text(one_stage_markdown_prompt)
            print("已将markdown格式转换为纯文本")
        except Exception as e:
            print(f"读取one_stage_prompt文件时出错：{str(e)}")
            exit()

        if not one_stage_prompt:
            print("错误：one_stage_prompt文件为空！")
            exit()

    # 提取answer.md中的JSON响应
    print("正在提取answer.md文档中的JSON响应...")
    answer_json_responses = extract_json_responses(answer_file)
    print(f"从answer.md文档中提取到 {len(answer_json_responses)} 个JSON响应")

    # 获取images文件夹中的所有图片文件
    image_files = get_image_files(images_dir)

    # 检查图片数量和JSON数量是否一致
    if len(image_files) != len(answer_json_responses):
        print(f"警告：图片数量({len(image_files)})和JSON响应数量({len(answer_json_responses)})不一致！")
        min_count = min(len(image_files), len(answer_json_responses))
        image_files = image_files[:min_count]
        answer_json_responses = answer_json_responses[:min_count]
        print(f"将使用前 {min_count} 个进行处理")

    # 生成 one_stage_response 文件夹和文件名
    now = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    response_file = os.path.join(one_stage_response_dir, f"{now}.md")
    output_lines = []

    # 新增：如果 use_enhance 为 True，最顶部插入说明
    if use_enhance:
        if use_pixel_connection:
            output_lines.append("使用'灰度阀门与像素增强'处理（含黑色像素粘连）\n\n")
        else:
            output_lines.append("使用'灰度阀门与像素增强'处理（不含黑色像素粘连）\n\n")

    # 预留准确率位置（稍后会在文件开头插入）
    output_lines.append(f"# 运行时间: {now}\n\n")
    output_lines.append(f"## 使用模型ID: {model_id}\n\n")
    output_lines.append(f"## 使用图片文件夹: {image_path_prefix}\n\n")
    output_lines.append(f"## 图片放大倍数: {scale}\n\n")

    # 添加使用的提示词
    output_lines.append(f"## 使用的one_stage_prompt\n\n")
    output_lines.append(f"{one_stage_prompt}\n\n")

    if not image_files:
        msg1 = "images文件夹中没有找到图片文件！"
        msg2 = "支持的格式：.jpg, .jpeg, .png, .gif, .webp"
        print(msg1)
        print(msg2)
        output_lines.append(msg1 + "\n")
        output_lines.append(msg2 + "\n")
        all_processed_results = []
    else:
        msg1 = f"找到 {len(image_files)} 张图片，开始逐个处理..."
        msg2 = f"使用的one_stage_prompt: {one_stage_prompt}"
        print(msg1)
        print(msg2)
        output_lines.append(msg1 + "\n")
        output_lines.append(msg2 + "\n")
        print("\n--- 开始本地处理图片（增强/缩放/编码） ---\n")
        api_key_from_client_init = "36c2aa0e-8b2b-4412-bc92-d3d1cef96b1b"

        # 准备本地处理任务
        local_tasks = []
        for i, image_path in enumerate(image_files):
            img_filename = os.path.basename(image_path)
            local_tasks.append({
                'image_path': image_path,
                'img_filename': img_filename,
                'use_enhance': use_enhance,
                'enhance_threshold': gray_threshold,
                'scale': scale,
                'use_pixel_connection': use_pixel_connection
            })

        # 使用多进程进行本地图片处理
        num_processes = os.cpu_count() if os.cpu_count() else 4
        print(f"正在使用 {num_processes} 个进程进行本地图片处理...")

        local_results = []
        with multiprocessing.Pool(processes=num_processes) as pool:
            # 使用imap代替map以支持进度条
            for result in tqdm(pool.imap(process_single_image_local, local_tasks),
                             total=len(local_tasks), desc="本地处理", unit="张"):
                local_results.append(result)

        # 准备API推理任务，只包含成功处理的图片
        api_tasks = []
        for i, (local_result, answer_json) in enumerate(zip(local_results, answer_json_responses)):
            if local_result['success']:
                api_tasks.append({
                    'img_filename': local_result['img_filename'],
                    'base64_image': local_result['base64_image'],
                    'one_stage_prompt': one_stage_prompt,
                    'answer_json': answer_json,
                    'client_api_key': api_key_from_client_init,
                    'model_id': model_id,  # 添加模型ID参数
                    'response_format': response_format,  # 添加response_format参数
                    'index': i + 1,
                    'image_path_prefix': f"../{image_path_prefix}/",
                    'image_path': local_result['image_path'],
                    'temperature': temperature,  # 添加temperature参数
                    'top_p': top_p,  # 添加top_p参数
                    'max_tokens': max_tokens  # 添加max_tokens参数
                })
            else:
                print(f"跳过处理失败的图片: {local_result['img_filename']}")

        print(f"本地处理完成: {len(api_tasks)}/{len(local_tasks)} 张图片成功处理")
        print(f"\n--- 开始并行API推理 ---\n")
        print(f"将使用 {num_processes} 个进程进行并行API推理。")

        # 使用进度条显示API推理进度
        all_processed_results = []
        with multiprocessing.Pool(processes=num_processes) as pool:
            # 使用imap代替map以支持进度条
            for result in tqdm(pool.imap(process_single_image_one_stage_api, api_tasks),
                             total=len(api_tasks), desc="API推理", unit="张"):
                all_processed_results.append(result)
        print("\n--- 并行API推理完成，合并结果 ---\n")
        for result in all_processed_results:
            output_lines.extend(result['output_lines'])
        sep = f"\n{'=' * 50}\n"
        output_lines.append(sep)
        output_lines.append("所有图片处理完成！\n")
        output_lines.append(sep)
        print(sep)
        print("所有图片处理完成！")
        print(sep)

    # 写入新内容到新文件
    with open(response_file, "w", encoding="utf-8") as f:
        f.writelines(output_lines)

    # 如果没有response_template.md，则将本次md文件复制为模板
    template_path = os.path.join(one_stage_response_dir, "response_template.md")
    if not os.path.exists(template_path):
        shutil.copyfile(response_file, template_path)

    # 比对错题功能 - 优先使用当前题型的round2_response_without_images文件夹下的模板，否则使用danxuanti的模板
    # 第一优先级：当前题型的round2_response_without_images文件夹下的response_template.md
    primary_template_path = os.path.join(question_dir, "round2_response_without_images", "response_template.md")
    # 第二优先级：danxuanti的round2_response_without_images文件夹下的response_template.md
    fallback_template_path = os.path.join("types", "danxuanti", "round2_response_without_images", "response_template.md")

    template_path = None
    template_source = ""

    if os.path.exists(primary_template_path):
        template_path = primary_template_path
        template_source = f"使用当前题型模板: {primary_template_path}"
    elif os.path.exists(fallback_template_path):
        template_path = fallback_template_path
        template_source = f"使用备用模板: {fallback_template_path}"

    wrong_items = []
    accuracy_str = ""
    if template_path:
        print(f"\n{template_source}")
        new_results = extract_json_responses_and_names(response_file)
        template_results = extract_json_responses_and_names(template_path)
        wrongs = []
        error_msgs = []
        # 检查数量
        if len(new_results) != len(template_results):
            error_msgs.append(f"数量不一致：本次{len(new_results)}，模板{len(template_results)}")
        # 只检查JSON内容，不检查文件名
        min_len = min(len(new_results), len(template_results))
        for i in range(min_len):
            new_name, new_json = new_results[i]
            tpl_name, tpl_json = template_results[i]

            # 直接比对JSON内容，不比对文件名
            if new_json != tpl_json:
                # 检查模板中的JSON是否为{}，如果是则跳过该题目的比较
                if tpl_json == "{}":
                    print(f"第 {i+1} 项: {new_name} - 模板JSON为空，跳过比较")
                    continue
                else:
                    # 在比较前，对两个JSON进行值序列比较
                    def compare_json_values(json_str1, json_str2):
                        """比较两个JSON的值序列，忽略键名差异、类型差异和空格差异"""
                        # 首先检查是否有None值
                        if json_str1 is None or json_str2 is None:
                            return json_str1 == json_str2

                        try:
                            obj1 = json.loads(json_str1)
                            obj2 = json.loads(json_str2)

                            # 按照题号数字顺序排序并提取值
                            def extract_question_number(key):
                                import re
                                match = re.search(r'\d+', str(key))
                                if match:
                                    return int(match.group())
                                return 0

                            # 对两个JSON对象按题号排序并提取值序列
                            sorted_items1 = sorted(obj1.items(), key=lambda x: extract_question_number(x[0]))
                            sorted_items2 = sorted(obj2.items(), key=lambda x: extract_question_number(x[0]))

                            # 标准化值，处理类型差异和空格
                            def normalize_value(val):
                                if isinstance(val, bool):
                                    return str(val).lower()
                                elif isinstance(val, str):
                                    # 去除首尾空格
                                    val_trimmed = val.strip()
                                    # 处理字符串形式的布尔值
                                    val_lower = val_trimmed.lower()
                                    if val_lower in ['true', 'false']:
                                        return val_lower
                                    return val_trimmed
                                else:
                                    return str(val).strip()

                            values1 = [normalize_value(item[1]) for item in sorted_items1]
                            values2 = [normalize_value(item[1]) for item in sorted_items2]

                            return values1 == values2
                        except:
                            # 如果JSON解析失败，回退到字符串比较
                            # 再次检查None值以避免AttributeError
                            if json_str1 is None or json_str2 is None:
                                return json_str1 == json_str2
                            return json_str1.strip() == json_str2.strip()

                    if not compare_json_values(new_json, tpl_json):
                        wrongs.append(f"第 {i+1} 项: {new_name}")
                        wrong_items.append(i+1)
                    

                        
        # 统计准确率
        total = min_len
        wrong_count = len(wrongs)
        accuracy = (total - wrong_count) / total if total > 0 else 1.0
        accuracy_str = f"准确率：{(accuracy*100):.2f}%  （({total} - {wrong_count}) / {total}）\n"

        # 保存准确率信息供返回使用
        accuracy_info = {
            'accuracy': accuracy,
            'accuracy_str': accuracy_str.strip(),
            'total': total,
            'wrong_count': wrong_count,
            'accuracy_percentage': f"{(accuracy*100):.2f}%"
        }

        # 构造新内容 - 按照新的格式要求：准确率置顶，然后是错题
        # 1. 准确率置顶
        new_top = f"## {accuracy_str}\n"

        # 2. 添加模型ID和图片文件夹信息
        model_line = f"**使用模型ID：** {model_id}\n\n"
        folder_line = f"**使用图片文件夹：** {image_path_prefix}\n\n"
        new_top += model_line + folder_line

        # 3. 错题信息
        new_top += "## 错题\n"
        if error_msgs:
            for msg in error_msgs:
                new_top += f"- {msg}\n"
        if wrongs:
            for w in wrongs:
                new_top += f"- {w}\n"
        if not error_msgs and not wrongs:
            new_top += "本次无错题。\n"
        new_top += "\n"
        new_top += f"## 纠错模板来源\n{template_source}\n\n"

        # 读取原有内容
        with open(response_file, "r", encoding="utf-8") as f:
            old_content = f.read()
        # 写回文件，准确率和错题在最前面
        with open(response_file, "w", encoding="utf-8") as f:
            f.write(new_top)
            f.write(old_content)
        # 控制台输出
        print(new_top)
        
        # 创建错题汇总文件
        if wrong_items:
            current_date = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            summary_file = os.path.join(one_stage_error_dir, f"error_summary_{current_date}.md")
            summary_lines = []
            # 头部：准确率、运行时间置顶，然后是模型ID和图片文件夹信息，最后是错题列表
            summary_lines.append(f"## 准确率：{(accuracy*100):.2f}%  （({total} - {wrong_count}) / {total}）\n\n")
            summary_lines.append(f"## 运行时间: {now}\n\n")
            # 添加模型ID和图片文件夹信息
            model_line2 = f"**使用模型ID：** {model_id}\n\n"
            folder_line2 = f"**使用图片文件夹：** {image_path_prefix}\n\n"
            summary_lines.append(model_line2)
            summary_lines.append(folder_line2)
            # 纠错模板来源
            summary_lines.append(f"## 纠错模板来源\n{template_source}\n\n")
            # 错题列表
            summary_lines.append(f"## 错题\n\n")
            for item in wrong_items:
                if item <= len(image_files):
                    img_name = os.path.basename(image_files[item-1])
                    summary_lines.append(f"- 第 {item} 项: {img_name}\n")
            # 只插入错题的详细内容块，格式与response下md完全一致
            for idx in wrong_items:
                if idx <= len(all_processed_results):
                    result = all_processed_results[idx-1]
                    # 修改图片路径，引用images文件夹内的图片
                    modified_output_lines = []
                    template_json = None

                    # 获取对应的模板JSON答案
                    if idx <= len(template_results):
                        template_json = template_results[idx-1][1]

                    for line in result['output_lines']:
                        # 将相对路径替换为引用images文件夹的路径
                        if f'../{image_path_prefix}/' in line:
                            line = line.replace(f'../{image_path_prefix}/', f'../images/')
                        modified_output_lines.append(line)

                        # 在图片显示后、模型回答前插入模板答案
                        if line.startswith(f"![{os.path.basename(image_files[idx-1])}]"):
                            # 添加模板答案部分
                            if template_json and template_json != "{}":
                                modified_output_lines.append("\n### response_template答案：\n")
                                modified_output_lines.append("```json\n")
                                modified_output_lines.append(f"{template_json}\n")
                                modified_output_lines.append("```\n")

                    summary_lines.extend(modified_output_lines)
            summary_lines.append("\n==================================================\n所有错题处理完成！\n==================================================\n")
            with open(summary_file, "w", encoding="utf-8") as f:
                f.writelines(summary_lines)
            print(f"已创建错题详细 summary.md 文件: {summary_file}")
    else:
        print(f"\n警告：未找到纠错模板文件！")
        print(f"尝试查找的路径：")
        print(f"1. {primary_template_path}")
        print(f"2. {fallback_template_path}")
        print("跳过纠错功能，仅保存处理结果。")

    print(f"结果已保存到：{response_file}")

    # 如果有准确率信息，返回准确率信息，否则返回默认值
    if 'accuracy_info' in locals():
        return True, accuracy_info  # 返回成功状态和准确率信息
    else:
        # 如果没有模板文件，返回默认准确率信息
        default_accuracy_info = {
            'accuracy': 0.0,
            'accuracy_str': '准确率：未知（无模板文件）',
            'total': 0,
            'wrong_count': 0,
            'accuracy_percentage': '未知'
        }
        return True, default_accuracy_info

if __name__ == "__main__":
    # 如果直接运行此脚本，则使用交互模式
    run_one_stage_test()